import { useBaseApi } from '/@/api/base';

// 押金管理接口服务
export const useDepositApi = () => {
	const baseApi = useBaseApi('deposit');
	return {
		// 创建押金账户
		add: (data: any) => baseApi.post('add', data),

		// 获取押金账户详情
		detail: (data: any) => baseApi.get('detail', data),

		// 押金缴费
		payment: (data: any) => baseApi.post('payment', data),

		// 押金退款
		refund: (data: any) => baseApi.post('refund', data),

		// 红冲（冲正）
		correctPaymentError: (data: any) => baseApi.post('correctPaymentError', data),

		// 开始结算
		startSettlement: (data: any) => baseApi.post('startSettlement', data),

		// 分页查询押金账户
		page: baseApi.page,

		// 获取下拉列表数据
		getDropdownData: (fromPage: Boolean = false, cancel: boolean = false) => baseApi.dropdownData({ fromPage }, cancel),
	};
};
