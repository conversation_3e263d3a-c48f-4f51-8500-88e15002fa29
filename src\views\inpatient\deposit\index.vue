<template>
	<div class="deposit-container">
		<!-- 账户查询 -->
		<el-card shadow="hover" style="margin-top: 5px">
			<template #header>
				<div class="card-header">
					<el-tag class="ml-2">账户查询</el-tag>
				</div>
			</template>
			<el-form label-width="auto">
				<el-row style="flex: 1">
					<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
						<el-form-item label="住院号" prop="inpatientNo">
							<el-input v-model="state.queryParams.inpatientNo" placeholder="请输入住院号" clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
						<el-form-item label="住院流水号" prop="inpatientSerialNo">
							<el-input v-model="state.queryParams.inpatientSerialNo" placeholder="请输入住院流水号" clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
						<el-form-item>
							<el-button-group>
								<el-button type="primary" icon="ele-Search" @click="handleSearchAccount">查询账户</el-button>
								<el-button icon="ele-Refresh" @click="resetQuery">重置</el-button>
							</el-button-group>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
		</el-card>

		<!-- 账户信息 -->
		<el-card shadow="hover" style="margin-top: 5px">
			<template #header>
				<div class="card-header">
					<el-tag class="ml-2">账户信息</el-tag>
				</div>
			</template>
			<el-descriptions :column="4" border>
				<el-descriptions-item label="住院流水号">{{ state.currentAccount.inpatientSerialNo }}</el-descriptions-item>
				<el-descriptions-item label="住院号">{{ state.currentAccount.inpatientNo }}</el-descriptions-item>
				<el-descriptions-item label="患者姓名">{{ state.currentAccount.patientName }}</el-descriptions-item>
				<el-descriptions-item label="账户状态">
					<g-sys-dict v-model="state.currentAccount.status" code="DepositAccountStatus" />
				</el-descriptions-item>
				<el-descriptions-item label="当前余额">
					<span>¥{{ (state.currentAccount.currentBalance || 0).toFixed(2) }} </span>
				</el-descriptions-item>
				<el-descriptions-item label="总缴费金额">
					<span>¥{{ (state.currentAccount.totalPaidAmount || 0).toFixed(2) }}</span>
				</el-descriptions-item>
				<el-descriptions-item label="总退款金额">
					<span>¥{{ (state.currentAccount.totalRefundedAmount || 0).toFixed(2) }}</span>
				</el-descriptions-item>
				<el-descriptions-item label="总使用金额">
					<span>¥{{ (state.currentAccount.totalUsedAmount || 0).toFixed(2) }}</span>
				</el-descriptions-item>
			</el-descriptions>
		</el-card>

		<!-- 缴费功能 -->
		<el-card shadow="hover" style="margin-top: 5px">
			<template #header>
				<div class="card-header">
					<el-tag class="ml-2">押金缴费</el-tag>
				</div>
			</template>
			<el-form :model="state.paymentForm" :rules="state.paymentRules" ref="paymentFormRef" label-width="auto">
				<el-row style="flex: 1">
					<el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
						<el-form-item label="缴费金额" prop="amount">
							<el-input-number v-model="state.paymentForm.amount" placeholder="请输入缴费金额" :min="0.01" :precision="2" style="width: 100%" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
						<el-form-item label="支付方式" prop="payType">
							<el-select v-model="state.paymentForm.payType" placeholder="请选择支付方式">
								<el-option v-for="item in state.paymentMethodList" :key="item.code" :label="item.name" :value="item.code" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
						<el-form-item label="备注" prop="remark">
							<el-input v-model="state.paymentForm.remark" placeholder="请输入备注信息（可选）" clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
						<el-form-item>
							<el-button type="success" icon="ele-Money" @click="handlePayment" :disabled="state.currentAccount.status === 3" :loading="state.paymentLoading"> 确认缴费 </el-button>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
		</el-card>

		<!-- 交易明细记录 -->
		<el-card class="full-table" shadow="hover" style="margin-top: 5px">
			<template #header>
				<div class="card-header">
					<el-tag class="ml-2">交易明细记录</el-tag>
				</div>
			</template>
			<el-table :data="state.transactions" v-loading="state.loading" border stripe height="400px">
				<el-table-column prop="invoiceNo" label="发票号" width="120" show-overflow-tooltip />
				<el-table-column prop="transactionType" label="交易类型" width="100" align="center">
					<template #default="scope">
						<g-sys-dict v-model="scope.row.transactionType" code="DepositTransactionType" />
					</template>
				</el-table-column>
				<el-table-column prop="amount" label="交易金额" width="120" align="right">
					<template #default="scope">
						<span>¥{{ (scope.row.amount || 0).toFixed(2) }} </span>
					</template>
				</el-table-column>
				<el-table-column prop="refundableAmount" label="可退金额" width="120" align="right">
					<template #default="scope">
						<span class="text-info">¥{{ (scope.row.refundableAmount || 0).toFixed(2) }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="channel" label="支付渠道" width="100" />
				<el-table-column prop="paymentMethod" label="支付方式" width="100">
					<template #default="scope">
						<el-tag>{{ getPaymentMethodName(scope.row.paymentMethod) }}</el-tag>
					</template>
				</el-table-column>

				<el-table-column prop="status" label="状态" width="80" align="center">
					<template #default="scope">
						<g-sys-dict v-model="scope.row.status" code="DepositTransactionStatus" />
					</template>
				</el-table-column>
				<el-table-column prop="createTime" label="交易时间" width="160" show-overflow-tooltip />
				<el-table-column prop="remark" label="备注" min-width="150" show-overflow-tooltip />
				<el-table-column label="操作" width="140" align="center" fixed="right" show-overflow-tooltip>
					<template #default="scope">
						<el-button
							:disabled="!(scope.row.transactionType === '0' && scope.row.refundableAmount > 0)"
							icon="ele-Delete"
							size="small"
							text
							type="warning"
							@click="handleRefundTransaction(scope.row)"
						>
							退款
						</el-button>
					</template>
				</el-table-column>
			</el-table>
		</el-card>

		<!-- 对话框组件 -->
		<RefundDialog ref="refundDialogRef" @reload-data="loadAccountDetail(state.currentAccount.inpatientRegisterId)" />
		<CorrectDialog ref="correctDialogRef" @reload-data="loadAccountDetail" />
	</div>
</template>

<script setup lang="ts" name="DepositManagement">
import { reactive, ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { useDepositApi } from '/@/api/inpatient/deposit';
import { useInpatientRegisterApi } from '/@/api/inpatient/inpatientRegister';
import { useBasicInfoApi } from '/@/api/shared/basicInfo';

// 引入对话框组件
import RefundDialog from '/@/views/inpatient/deposit/components/RefundDialog.vue';
import CorrectDialog from '/@/views/inpatient/deposit/components/CorrectDialog.vue';

// API实例
const depositApi = useDepositApi();
const inpatientRegisterApi = useInpatientRegisterApi();
const basicInfoApi = useBasicInfoApi();

// 响应式数据
const state = reactive({
	inpatientRegisterInfo: {} as any,
	loading: false,
	paymentLoading: false,
	currentAccount: {} as any,
	transactions: [] as any[],
	queryParams: {
		inpatientNo: '',
		inpatientSerialNo: '',
	},
	paymentForm: {
		amount: undefined as number | undefined,
		payType: '',
		remark: '',
	},
	paymentRules: {
		amount: [
			{ required: true, message: '请输入缴费金额', trigger: 'blur' },
			{ type: 'number', min: 0.01, message: '缴费金额必须大于0', trigger: 'blur' },
		],
		payType: [{ required: true, message: '请选择支付方式', trigger: 'change' }],
	},
	paymentMethodList: [] as any[],
});

// 引用
const paymentFormRef = ref();
const refundDialogRef = ref();
const correctDialogRef = ref();

// 页面加载时
onMounted(async () => {
	// 获取支付方式列表
	let res = await basicInfoApi.getPayMethods({});
	state.paymentMethodList = res.data.result ?? [];
});

// 获取金额前缀
const getAmountPrefix = (type: string) => {
	if (type === '0') return '+'; // 缴费
	if (type === '1' || type === '2' || type === '3') return '-'; // 退款、使用、冲正
	return '';
};
const getPaymentMethodName = (value: any) => {
	const paymentMethod = state.paymentMethodList.find((item: any) => item.code === value);
	return paymentMethod ? paymentMethod.name : '';
};

// 查询账户
const handleSearchAccount = async () => {
	if (!state.queryParams.inpatientNo && !state.queryParams.inpatientNo) {
		ElMessage.warning('请输入住院号或住院流水号');
		return;
	}

	state.loading = true;
	try {
		const response = await inpatientRegisterApi.detail(state.queryParams);
		if (response.data) {
			// 如果找到账户，加载详细信息
			state.inpatientRegisterInfo = response.data.result;
			await loadAccountDetail(response.data.result.id);
			ElMessage.success('账户查询成功');
		} else {
			ElMessage.warning('未找到对应的押金账户');
			state.currentAccount = {};
			state.transactions = [];
		}
	} catch (error) {
		console.error('查询账户失败:', error);
		state.currentAccount = {};
		state.transactions = [];
	} finally {
		state.loading = false;
	}
};

// 加载账户详情
const loadAccountDetail = async (accountId: number) => {
	state.loading = true;
	try {
		const response = await depositApi.detail({ inpatientRegisterId: accountId });
		if (response.data.result) {
			state.currentAccount = response.data.result;
			state.currentAccount.inpatientSerialNo = state.inpatientRegisterInfo.inpatientSerialNo;
			state.currentAccount.patientName = state.inpatientRegisterInfo.patientName;
			state.transactions = response.data.result.transactions || [];
		}
	} catch (error) {
		console.error('加载账户详情失败:', error);
	} finally {
		state.loading = false;
	}
};

// 重置查询
const resetQuery = () => {
	state.queryParams = {
		inpatientNo: '',
		inpatientSerialNo: '',
	};
	state.currentAccount = {};
	state.transactions = [];
};

// 押金缴费
const handlePayment = async () => {
	if (!paymentFormRef.value) return;

	try {
		await paymentFormRef.value.validate();
	} catch (error) {
		ElMessage.warning('请完善缴费信息');
		return;
	}

	state.paymentLoading = true;
	try {
		const paymentData = {
			accountId: state.currentAccount.id,
			amount: state.paymentForm.amount,
			payType: state.paymentForm.payType,
			remark: state.paymentForm.remark,
		};

		await depositApi.payment(paymentData);
		ElMessage.success('缴费成功');
		// 重置表单
		state.paymentForm = {
			amount: undefined,
			payType: '',
			remark: '',
		};
		paymentFormRef.value.resetFields();
		// 重新加载账户详情
		await loadAccountDetail(state.currentAccount.inpatientRegisterId);
	} catch (error) {
		console.error('缴费失败:', error);
	} finally {
		state.paymentLoading = false;
	}
};

// 交易退款
const handleRefundTransaction = (transaction: any) => {
	refundDialogRef.value?.openDialog(state.currentAccount, transaction);
};

// 红冲操作
const handleCorrect = (transaction: any) => {
	correctDialogRef.value?.openDialog(transaction);
};
</script>

<style lang="scss" scoped>
.deposit-container {
	display: flex;
	flex-direction: column;
	height: 100vh;
	/* 使容器占满整个视口高度 */
}

:deep(.el-input),
:deep(.el-select),
:deep(.el-input-number) {
	width: 100%;
}
:deep(.el-card__header) {
	padding: 5px;
}

:deep(.el-card__body) {
	padding: 10px;
}

.card-header {
	display: flex;
	align-items: center;
}
</style>
